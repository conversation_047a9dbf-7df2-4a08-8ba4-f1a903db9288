<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .carousel-test {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .carousel-test img {
            width: 150px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            border: 2px solid #ddd;
        }
        .api-result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API测试页面</h1>
        
        <div class="test-section">
            <h3>1. API连接测试</h3>
            <button onclick="testAPI()">测试API调用</button>
            <div id="api-status"></div>
            <div id="api-result" class="api-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 企业名称测试</h3>
            <p>当前企业名称: <span id="ent-name">未获取</span></p>
            <p>企业代码: <span id="ent-code">未获取</span></p>
        </div>
        
        <div class="test-section">
            <h3>3. 轮播图测试</h3>
            <p>轮播图数量: <span id="carousel-count">0</span></p>
            <div id="carousel-images" class="carousel-test"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 图片URL测试</h3>
            <div id="image-urls"></div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const statusDiv = document.getElementById('api-status');
            const resultDiv = document.getElementById('api-result');
            
            statusDiv.innerHTML = '<div class="status">正在调用API...</div>';
            
            try {
                const response = await fetch('http://localhost:3000/api');
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="status success">✅ API调用成功</div>';
                    resultDiv.textContent = JSON.stringify(result, null, 2);
                    
                    // 更新企业信息
                    updateEntInfo(result.data);
                    
                    // 更新轮播图
                    updateCarouselTest(result.data);
                    
                } else {
                    statusDiv.innerHTML = '<div class="status error">❌ API调用失败</div>';
                    resultDiv.textContent = JSON.stringify(result, null, 2);
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="status error">❌ 连接失败: ' + error.message + '</div>';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }
        
        function updateEntInfo(data) {
            const entNameSpan = document.getElementById('ent-name');
            const entCodeSpan = document.getElementById('ent-code');
            
            entNameSpan.textContent = data.EntName || '未知企业';
            entCodeSpan.textContent = data.EntCode || '未知代码';
        }
        
        function updateCarouselTest(data) {
            const carouselImages = data.CarouselImages || [];
            const entCode = data.EntCode || '05580002';
            
            const countSpan = document.getElementById('carousel-count');
            const imagesDiv = document.getElementById('carousel-images');
            const urlsDiv = document.getElementById('image-urls');
            
            countSpan.textContent = carouselImages.length;
            
            // 清空现有内容
            imagesDiv.innerHTML = '';
            urlsDiv.innerHTML = '';
            
            if (carouselImages.length === 0) {
                imagesDiv.innerHTML = '<p>没有轮播图数据</p>';
                return;
            }
            
            // 生成图片和URL列表
            carouselImages.forEach((imageId, index) => {
                const imageUrl = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${imageId}`;
                
                // 创建图片元素
                const img = document.createElement('img');
                img.src = imageUrl;
                img.alt = `轮播图${index + 1}`;
                img.title = `图片ID: ${imageId}`;
                
                // 添加错误处理
                img.onerror = function() {
                    this.style.border = '2px solid red';
                    this.alt = '图片加载失败';
                };
                
                img.onload = function() {
                    this.style.border = '2px solid green';
                };
                
                imagesDiv.appendChild(img);
                
                // 创建URL信息
                const urlInfo = document.createElement('div');
                urlInfo.innerHTML = `
                    <p><strong>图片${index + 1}:</strong></p>
                    <p>ID: ${imageId}</p>
                    <p>URL: <a href="${imageUrl}" target="_blank">${imageUrl}</a></p>
                    <hr>
                `;
                urlsDiv.appendChild(urlInfo);
            });
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', function() {
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
