<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--=============== FAVICON ===============-->
    <link rel="shortcut icon" href="./assets/img/favicon.png" type="image/x-icon">

    <!--=============== BOXICONS ===============-->
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>

    <!--=============== SWIPER CSS ===============-->
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css">
    
    <!--=============== CSS ===============-->
    <link rel="stylesheet" href="assets/css/styles.css">

    <title> 李晓鹏的响应式个人简历 </title>
</head>

<body>
    <!--=============== HEADER ===============-->
    <header class="header" id="header">
        <nav class="nav container">
            <a href="#" class="nav__logo">安徽美誉制药有限公司</a>
            <!-- 导航菜单 -->
            <div class="nav__menu">
                <ul class="nav__list">
                    <li class="nav__item">
                        <a href="#home" class="nav__link active-link">
                            <i class='bx bx-home-heart'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#about" class="nav__link">
                            <i class='bx bx-user'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#skills " class="nav__link">
                            <i class='bx bx-book-heart'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#work" class="nav__link">
                            <i class='bx bx-briefcase-alt-2'></i>
                        </a>
                    </li>
                    <li class="nav__item">
                        <a href="#contact" class="nav__link">
                            <i class='bx bx-message-square-detail'></i>
                        </a>
                    </li>
                </ul>
            </div>
            <!-- 主题切换按钮 -->
            <i class='bx bx-moon change-theme' id="theme-button"></i>
        </nav>
    </header>

    <!--=============== MAIN ===============-->
    <main class="main">
        <!--=============== HOME ===============-->
        <section class="home section" id="home">
            <div class="home__container container grid">
                <div class="home__carousel">
                    <div class="carousel-container">
                        <div class="carousel-slides">
                            <img src="assets/img/Image1.jpg" alt="轮播图1" class="carousel-img active">
                            <img src="assets/img/Image2.jpg" alt="轮播图2" class="carousel-img">
                            <img src="assets/img/Image3.jpg" alt="轮播图3" class="carousel-img">
                            <img src="assets/img/Image4.jpg" alt="轮播图4" class="carousel-img">
                        </div>
                        <div class="carousel-dots">
                            <span class="dot active" data-slide="0"></span>
                            <span class="dot" data-slide="1"></span>
                            <span class="dot" data-slide="2"></span>
                            <span class="dot" data-slide="3"></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== ABOUT ===============-->
        <section class="about section" id="about">
            <div class="about__container grid">
                <div class="about__expandable">
                    <div class="expandable__item">
                        <div class="expandable__header" data-target="product-info">
                            <h3 class="expandable__title">产品信息</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="product-info">
                            <div class="expandable__body">
                                <div class="product-info-form">
                                    <div class="form-row">
                                        <label class="form-label">品名</label>
                                        <input type="text" class="form-input" id="product-name" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">规格</label>
                                        <input type="text" class="form-input" id="product-spec" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">产地</label>
                                        <input type="text" class="form-input" id="product-origin" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">执行标准</label>
                                        <input type="text" class="form-input" id="product-standard" value="加载中..." readonly>
                                    </div>
                                    <div class="form-row">
                                        <label class="form-label">包装规格</label>
                                        <input type="text" class="form-input" id="product-package" value="加载中..." readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="production-trace">
                            <h3 class="expandable__title">生产追溯</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="production-trace">
                            <div class="expandable__body">
                                <!-- 生产追溯内容 -->
                                <div class="production-trace-container">
                                    <!-- 基本信息卡片 -->
                                    <div class="trace-info-card">
                                        <div class="form-row">
                                            <label class="form-label">生产批号</label>
                                            <input type="text" class="form-input" id="production-batch-no" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">生产日期</label>
                                            <input type="text" class="form-input" id="production-date" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">保质期</label>
                                            <input type="text" class="form-input" id="expiration-date" value="加载中..." readonly>
                                        </div>
                                    </div>

                                    <!-- 时间线 -->
                                    <div class="timeline-container">
                                        <div class="timeline-item" id="timeline-product">
                                            <div class="timeline-date">
                                                <span id="product-date">2025.08.01</span> 产品信息
                                            </div>
                                            <div class="timeline-content">
                                                <div class="timeline-details">
                                                    <div class="detail-row">
                                                        <span class="detail-label">产品信息：</span>
                                                        <span class="detail-value" id="timeline-product-name">芡实2</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">批号：</span>
                                                        <span class="detail-value" id="timeline-batch-no">25080101A</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">产地：</span>
                                                        <span class="detail-value" id="timeline-origin">广东省肇庆市</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">净重：</span>
                                                        <span class="detail-value" id="timeline-weight">119.3</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">规格：</span>
                                                        <span class="detail-value" id="timeline-spec">净制</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">生产日期：</span>
                                                        <span class="detail-value" id="timeline-prod-date">2025.08.01</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">保质期：</span>
                                                        <span class="detail-value" id="timeline-shelf-life">24个月</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">生产许可证</span>
                                                        <span class="detail-value" id="timeline-license">皖20160093</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">执行标准</span>
                                                        <span class="detail-value" id="timeline-standard">《中国药典》2020版一部及四部</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">贮藏：</span>
                                                        <span class="detail-value" id="timeline-storage">置通风干燥处，防蛀。</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">生产企业：</span>
                                                        <span class="detail-value" id="timeline-company">安徽美誉制药有限公司</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">生产地址：</span>
                                                        <span class="detail-value" id="timeline-address">安徽省亳州市谯城经济开发区张良路东侧、亳芍路南侧</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="timeline-item" id="timeline-base">
                                            <div class="timeline-date">
                                                <span id="base-date">2025.07.20</span> 基地信息
                                            </div>
                                            <div class="timeline-content">
                                                <div class="timeline-details">
                                                    <div class="detail-row">
                                                        <span class="detail-label">种源信息：</span>
                                                        <span class="detail-value" id="timeline-seed-info">睡莲科植物芡Euryale ferox Salisb.的干燥成熟种仁</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">环境信息：</span>
                                                        <span class="detail-value" id="timeline-env-info">生在池塘、湖沼中</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">田间管理：</span>
                                                        <span class="detail-value" id="timeline-field-mgmt">4月份种植，种子育苗，复合肥、腐熟有机肥、尿素</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">采收信息：</span>
                                                        <span class="detail-value" id="timeline-harvest-info">11月份采收，干燥成熟种仁、人工</span>
                                                    </div>
                                                    <div class="detail-row">
                                                        <span class="detail-label">产地初加工：</span>
                                                        <span class="detail-value" id="timeline-initial-process">秋末冬初采收成熟果实，除去果皮，取出种子，洗净，再除去硬壳（外种皮），晒干</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="company-intro">
                            <h3 class="expandable__title">企业介绍</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="company-intro">
                            <div class="expandable__body">
                                <!-- 企业介绍内容将在这里显示 -->
                                <p>企业介绍详细内容...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== SKILLS ===============-->
        <section class="skills section" id="skills">
            <span class="section__subtitle">我都会哪些？</span>
            <h2 class="section__title">我掌握的技术</h2>

            <div class="skills__container container grid">

                <div class="skills__content">
                    <h3 class="skills__title">前端开发</h3>

                    <div class="skills__box">
                        <!-- 技能树1 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">HTML</h3>
                                    <span class="skills__level">熟练</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">CSS</h3>
                                    <span class="skills__level">熟练</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">JavaScript</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                        <!-- 技能树2 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Vue2/Vue3</h3>
                                    <span class="skills__level">入门</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">微信小程序</h3>
                                    <span class="skills__level">入门</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">ElementUI</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="skills__content">
                    <h3 class="skills__title">后端开发</h3>

                    <div class="skills__box">
                        <!-- 技能树1 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Java</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Pyhton</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Node Js</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                        <!-- 技能树2 -->
                        <div class="skills__group">
                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">MySQL</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">微信小程序云开发</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>

                            <div class="skills__data">
                                <i class='bx bxs-badge-check'></i>

                                <div>
                                    <h3 class="skills__name">Firebase</h3>
                                    <span class="skills__level">掌握</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== SERVICES ===============-->
        <section class="services section">
            <span class="section__subtitle">我能做什么？</span>
            <h2 class="section__title">我可以运用这些技术</h2>

            <div class="services__container container grid">
                <div class="services__card">
                    <h3 class="services__title">网页设计与制作</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">网页设计与制作</h3>
                            <p class="services__modal-description">
                                xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="services__card">
                    <h3 class="services__title">Vue项目开发</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">Vue项目开发</h3>
                            <p class="services__modal-description">
                                xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="services__card">
                    <h3 class="services__title">小程序开发</h3>
                    <span class="services__button">
                        <i class='bx bx-right-arrow-alt services__icon'></i>查看更多
                    </span>
                    <div class="services__modal">
                        <div class="services__modal-content">
                            <i class='bx bx-x services__modal-close'></i>
                            <h3 class="services__modal-title">小程序开发</h3>
                            <p class="services__modal-description">
                                我说这是嘛呀xxxxxxxxxxxxxxxxxxxxxxx
                            </p>

                            <ul class="services__modal-list">
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        一个xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        两个xxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                                <li class="services__modal-item">
                                    <i class='bx bx-check services__modal-icon'></i>
                                    <p class="services__modal-info">
                                        xxxxxxxxxxxxxxxxxxxxxxx
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== WORK ===============-->
        <section class="work section" id="work">
            <span class="section__subtitle">我做过哪些？</span>
            <h2 class="section__title">我最近的项目</h2>

            <div class="work__filters">
                <span class="work__item active-work" data-filter="all">全部</span>
                <span class="work__item" data-filter=".web">网页</span>
                <span class="work__item" data-filter=".mini">小程序</span>
                <span class="work__item" data-filter=".admin">后台管理系统</span>
            </div>

            <div class="work__container container grid">
                <div class="work__card mix web">
                    <img src="assets/img/work1.jpg" alt="" class="work__img">
                    <h3 class="work__title">网页</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix mini">
                    <img src="assets/img/work2.jpg" alt="" class="work__img">
                    <h3 class="work__title">小程序</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix mini">
                    <img src="assets/img/work3.jpg" alt="" class="work__img">
                    <h3 class="work__title">小程序</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix web">
                    <img src="assets/img/work4.jpg" alt="" class="work__img">
                    <h3 class="work__title">网页</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
                <div class="work__card mix admin">
                    <img src="assets/img/work5.jpg" alt="" class="work__img">
                    <h3 class="work__title">后台管理系统</h3>
                    <a href="#" class="work__button">
                        <i class='bx bx-right-arrow-alt work__icon'></i> 项目演示
                    </a>
                </div>
            </div>
        </section>

        <!--=============== TESTIMONIALS ===============-->
        <section class="testimonial section">
            <span class="section__subtitle">认识我的人是这么说的</span>
            <h2 class="section__title">我的评价</h2>

            <div class="testimonial__container container swiper">
                <div class=" swiper-wrapper">
                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial1.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">舍友陈同学</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>

                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial2.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">辅导员张老师</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>

                    <div class="testimonial__card swiper-slide">
                        <img src="assets/img/testimonial3.png" alt="" class="testimonial__img">
                        <h3 class="testimonial__name">同事小林</h3>
                        <p class="testimonial__description">
                            他是一个XXXXXXXXXXXXXXXXXXXXXXXXXXX
                        </p>
                    </div>
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </section>

        <!--=============== CONTACT ===============-->
        <section class="contact section" id="contact">
            <span class="section__subtitle">如何与我联系？</span>
            <h2 class="section__title">我的联系方式</h2>

            <div class="contact__container container grid">
                <div class="contactt__content">
                    <h3 class="contactt__title">与我交流</h3>

                    <div class="contact__info">
                        <div class="contact__card">
                            <i class='bx bx-phone-call contact__card-icon'></i>
                            <h3 class="contact__card-title">电话</h3>
                            <span class="contact__card-data">123456789</span>

                            <a href="" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                        <div class="contact__card">
                            <i class='bx bx-mail-send contact__card-icon'></i>
                            <h3 class="contact__card-title">邮箱</h3>
                            <span class="contact__card-data"><EMAIL></span>

                            <a href="mailto:<EMAIL>" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                        <div class="contact__card">
                            <i class='bx bxl-whatsapp contact__card-icon'></i>
                            <h3 class="contact__card-title">微信</h3>
                            <span class="contact__card-data">ImPump_</span>

                            <a href="" target="_blank" class="contact__button">
                                <i class='bx bx-right-arrow-alt contact__button-icon'></i>现在联系
                            </a>
                        </div>
                    </div>
                </div>
        </section>
    </main>

    <!--=============== FOOTER ===============-->
    <footer class="footer">
        <div class="footer__container container">
            <h1 class="footer__title">李晓鹏</h1>
            <ul class="footer__list">
                <li>
                    <a href="#about" class="footer__link">我的信息</a>
                </li>
                <li>
                    <a href="#skills" class="footer__link">我的技能</a>
                </li>
                <li>
                    <a href="#work" class="footer__link">我的项目</a>
                </li>
            </ul>

            <ul class="footer__social">
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-facebook'></i>
                </a>
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-instagram'></i>
                </a>
                <a href="" target="_blank" class="footer__social-link">
                    <i class='bx bxl-twitter' ></i>
                </a>
            </ul>

            <span class="footer__copy">
                &#169;ImPump. All rights reserved
            </span>
        </div>
    </footer>

    <!--=============== SCROLLREVEAL ===============-->
    <script src="assets/js/scrollreveal.min.js"></script>

    <!--=============== SWIPER JS ===============-->
    <script src="assets/js/swiper-bundle.min.js"></script>

    <!--=============== MIXITUP FILTER ===============-->
    <script src="assets/js/mixitup.min.js"></script>

    <!--=============== MAIN JS ===============-->
    <script src="assets/js/main.js"></script>

    <!--=============== API CALL SCRIPT ===============-->
    <script>
        // 更新页面产品信息的函数
        function updateProductInfo(apiData) {
            try {
                // 获取产品名称
                const productName = apiData.Product || '未知产品';

                // 获取产品属性
                const productProperties = apiData.ProductProperties || {};

                // 更新页面元素
                const productNameElement = document.getElementById('product-name');
                const productSpecElement = document.getElementById('product-spec');
                const productOriginElement = document.getElementById('product-origin');
                const productStandardElement = document.getElementById('product-standard');
                const productPackageElement = document.getElementById('product-package');

                if (productNameElement) {
                    productNameElement.value = productName;
                }

                if (productSpecElement) {
                    productSpecElement.value = productProperties['基础信息_规格'] || '未知规格';
                }

                if (productOriginElement) {
                    productOriginElement.value = productProperties['基础信息_产地'] || '未知产地';
                }

                if (productStandardElement) {
                    productStandardElement.value = productProperties['基础信息_执行标准'] || '未知标准';
                }

                if (productPackageElement) {
                    productPackageElement.value = productProperties['基础信息_包装规格'] || '未知包装';
                }

                console.log('✅ 页面产品信息已更新');
                console.log('品名:', productName);
                console.log('规格:', productProperties['基础信息_规格']);
                console.log('产地:', productProperties['基础信息_产地']);
                console.log('执行标准:', productProperties['基础信息_执行标准']);
                console.log('包装规格:', productProperties['基础信息_包装规格']);

            } catch (error) {
                console.error('更新产品信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 更新生产追溯信息的函数
        function updateProductionTrace(apiData) {
            try {
                // 更新基本信息卡片
                const batchNoElement = document.getElementById('production-batch-no');
                const productionDateElement = document.getElementById('production-date');
                const expirationDateElement = document.getElementById('expiration-date');

                if (batchNoElement) {
                    batchNoElement.value = apiData.ProductionBatchNo || '未知批号';
                }

                if (productionDateElement) {
                    productionDateElement.value = apiData.ProductionDate || '未知日期';
                }

                if (expirationDateElement) {
                    expirationDateElement.value = apiData.ExpirationDate || '未知日期';
                }

                // 获取生命周期数据
                const lifeCycles = apiData.LifeCycles || {};

                // 更新产品信息时间线
                const productStartTime = lifeCycles['产品信息_开始时间'] || '2025.08.01';
                const productDateElement = document.getElementById('product-date');

                if (productDateElement) {
                    productDateElement.textContent = productStartTime;
                }

                // 更新产品信息详细内容
                const productInfoElements = {
                    'timeline-product-name': lifeCycles['产品信息_产品信息：'] || apiData.Product,
                    'timeline-batch-no': lifeCycles['产品信息_批号：'] || apiData.ProductionBatchNo,
                    'timeline-origin': lifeCycles['产品信息_产地：'],
                    'timeline-weight': lifeCycles['产品信息_净重：'],
                    'timeline-spec': lifeCycles['产品信息_规格：'],
                    'timeline-prod-date': lifeCycles['产品信息_生产日期：'],
                    'timeline-shelf-life': lifeCycles['产品信息_保质期：'],
                    'timeline-license': lifeCycles['产品信息_生产许可证'],
                    'timeline-standard': lifeCycles['产品信息_执行标准'],
                    'timeline-storage': lifeCycles['产品信息_贮藏：'],
                    'timeline-company': lifeCycles['产品信息_生产企业：'],
                    'timeline-address': lifeCycles['产品信息_生产地址：']
                };

                Object.entries(productInfoElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element && value) {
                        element.textContent = value;
                    }
                });

                // 更新基地信息时间线
                const baseStartTime = lifeCycles['基地信息_开始时间'] || '2025.07.20';
                const baseDateElement = document.getElementById('base-date');

                if (baseDateElement) {
                    baseDateElement.textContent = baseStartTime;
                }

                // 更新基地信息详细内容
                const baseInfoElements = {
                    'timeline-seed-info': lifeCycles['基地信息_种源信息：'],
                    'timeline-env-info': lifeCycles['基地信息_环境信息：'],
                    'timeline-field-mgmt': lifeCycles['基地信息_田间管理：'],
                    'timeline-harvest-info': lifeCycles['基地信息_采收信息：'],
                    'timeline-initial-process': lifeCycles['基地信息_产地初加工：']
                };

                Object.entries(baseInfoElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element && value) {
                        element.textContent = value;
                    }
                });

                console.log('✅ 生产追溯信息已更新');
                console.log('生产批号:', apiData.ProductionBatchNo);
                console.log('生产日期:', apiData.ProductionDate);
                console.log('保质期:', apiData.ExpirationDate);

            } catch (error) {
                console.error('更新生产追溯信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const traceElements = ['production-batch-no', 'production-date', 'expiration-date'];
                traceElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 调用昆鹏360接口的函数（通过本地代理服务器）
        async function callKunpengAPI() {
            const proxyUrl = 'http://localhost:3000/api';

            try {
                console.log('正在通过代理服务器调用API:', proxyUrl);

                const response = await fetch(proxyUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('代理服务器响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('=== API调用结果 ===');
                console.log('完整响应:', result);

                if (result.success) {
                    console.log('=== 原始API返回数据 ===');
                    console.log(result.rawData);

                    console.log('=== 解析后的数据 ===');
                    console.log(result.data);

                    console.log('=== API响应状态码 ===');
                    console.log(result.statusCode);

                    console.log('=== API响应头 ===');
                    console.log(result.headers);

                    // 🎯 关键：更新页面产品信息和生产追溯信息
                    updateProductInfo(result.data);
                    updateProductionTrace(result.data);

                } else {
                    console.error('API调用失败:', result.error);
                    // 显示错误状态
                    const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                    elements.forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.value = 'API调用失败';
                        }
                    });
                }

                return result;

            } catch (error) {
                console.error('调用代理服务器时发生错误:', error);
                console.error('错误详情:', error.message);
                console.log('请确保代理服务器正在运行 (node api-server.js)');

                // 显示错误状态
                const elements = ['product-name', 'product-spec', 'product-origin', 'product-standard', 'product-package'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '连接失败';
                    }
                });

                return null;
            }
        }

        // 页面加载完成后自动调用API
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始调用API...');

            // 首先尝试调用真实API
            callKunpengAPI().then(result => {
                // 如果API调用失败，使用demo数据作为备用
                if (!result || !result.success) {
                    console.log('API调用失败，使用demo数据作为备用...');
                    testWithDemoData();
                }
            }).catch(error => {
                console.log('API调用出错，使用demo数据作为备用...');
                testWithDemoData();
            });
        });

        // 也可以手动调用
        window.callKunpengAPI = callKunpengAPI;

        // 测试函数：使用demo.json数据更新页面
        async function testWithDemoData() {
            try {
                const response = await fetch('./assets/demo.json');
                const demoData = await response.json();

                console.log('=== 使用demo.json测试数据 ===');
                console.log(demoData);

                // 更新产品信息和生产追溯
                updateProductInfo(demoData);
                updateProductionTrace(demoData);

                console.log('✅ demo数据已加载到页面');
            } catch (error) {
                console.error('加载demo数据失败:', error);
            }
        }

        // 暴露测试函数到全局
        window.testWithDemoData = testWithDemoData;
    </script>
</body>

</html>