<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间线样式演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        
        .demo-description {
            text-align: center;
            color: #666;
            margin-bottom: 3rem;
            font-size: 1.1rem;
        }
        
        /* 时间线样式 - 基于图片设计 */
        .timeline-container {
            position: relative;
            padding-left: 3rem;
            margin-top: 2rem;
        }

        /* 垂直连接线 */
        .timeline-container::before {
            content: '';
            position: absolute;
            left: 1.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #e0e0e0;
            z-index: 1;
        }

        /* 时间线项目 */
        .timeline-item {
            position: relative;
            margin-bottom: 2.5rem;
            background-color: #ffffff;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        /* 圆形标记点 */
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -2.25rem;
            top: 1.5rem;
            width: 12px;
            height: 12px;
            background-color: #ffffff;
            border: 3px solid #4a90e2;
            border-radius: 50%;
            z-index: 2;
            box-shadow: 0 0 0 4px #ffffff;
        }

        /* 连接箭头 */
        .timeline-item::after {
            content: '';
            position: absolute;
            left: -1.5rem;
            top: 1.5rem;
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            border-right: 12px solid #ffffff;
            z-index: 1;
        }

        /* 时间线日期头部 */
        .timeline-date {
            background-color: #f8f9fa;
            padding: 1.25rem 1.5rem 1rem;
            color: #6c757d;
            font-size: 0.875rem;
            font-weight: 600;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .timeline-date span {
            color: #495057;
            font-weight: 700;
            font-size: 1rem;
        }

        /* 时间线内容区域 */
        .timeline-content {
            padding: 1.5rem;
            background-color: #ffffff;
        }

        .timeline-details {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        /* 详情行 */
        .detail-row {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f8f9fa;
            gap: 0.5rem;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .detail-value {
            color: #212529;
            font-size: 0.875rem;
            line-height: 1.4;
            text-align: left;
            word-break: break-word;
            padding-left: 1rem;
        }

        /* 响应式设计 */
        @media screen and (max-width: 768px) {
            .timeline-container {
                padding-left: 2rem;
            }

            .timeline-container::before {
                left: 1rem;
            }

            .timeline-item::before {
                left: -1.75rem;
            }

            .timeline-item::after {
                left: -1rem;
            }

            .timeline-date {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .detail-row {
                flex-direction: column;
                gap: 0.25rem;
                align-items: flex-start;
            }

            .detail-label {
                min-width: auto;
                margin-bottom: 0.25rem;
            }

            .detail-value {
                text-align: left;
                max-width: 100%;
            }
        }

        @media screen and (max-width: 480px) {
            .timeline-container {
                padding-left: 1.5rem;
            }

            .timeline-container::before {
                left: 0.75rem;
            }

            .timeline-item::before {
                left: -1.5rem;
            }

            .timeline-item::after {
                left: -0.75rem;
            }

            .timeline-date {
                padding: 1rem;
            }

            .timeline-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>时间线样式演示</h1>
        <p class="demo-description">这是基于您提供的图片设计的时间线样式，包含垂直连接线、圆形标记点和信息块</p>
        
        <div class="timeline-container">
            <div class="timeline-item">
                <div class="timeline-date">
                    <span>2025.08.01</span> 产品信息
                </div>
                <div class="timeline-content">
                    <div class="timeline-details">
                        <div class="detail-row">
                            <span class="detail-label">产品信息：</span>
                            <span class="detail-value">芡实</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">批号：</span>
                            <span class="detail-value">25080101A</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">产地：</span>
                            <span class="detail-value">广东省肇庆市</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">净重：</span>
                            <span class="detail-value">119.3</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">规格：</span>
                            <span class="detail-value">净制</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">生产日期：</span>
                            <span class="detail-value">2025.08.01</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">保质期：</span>
                            <span class="detail-value">24个月</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">生产许可证：</span>
                            <span class="detail-value">皖20160093</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">执行标准：</span>
                            <span class="detail-value">《中国药典》2020版一部及四部</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">贮藏：</span>
                            <span class="detail-value">置通风干燥处，防蛀。</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">生产企业：</span>
                            <span class="detail-value">安徽美誉制药有限公司</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">生产地址：</span>
                            <span class="detail-value">安徽省亳州市谯城经济开发区张良路东侧、亳芍路南侧</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-date">
                    <span>2025.07.20</span> 基地信息
                </div>
                <div class="timeline-content">
                    <div class="timeline-details">
                        <div class="detail-row">
                            <span class="detail-label">种源信息：</span>
                            <span class="detail-value">睡莲科植物芡Euryale ferox Salisb.的干燥成熟种仁</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">环境信息：</span>
                            <span class="detail-value">生在池塘、湖沼中</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">田间管理：</span>
                            <span class="detail-value">4月份种植，种子育苗，复合肥、腐熟有机肥、尿素</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">采收信息：</span>
                            <span class="detail-value">11月份采收，干燥成熟种仁、人工</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">产地初加工：</span>
                            <span class="detail-value">秋末冬初采收成熟果实，除去果皮，取出种子，洗净，再除去硬壳（外种皮），晒干</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 3rem; color: #666;">
            <p>鲲鹏数字化追溯平台</p>
        </div>
    </div>
</body>
</html> 